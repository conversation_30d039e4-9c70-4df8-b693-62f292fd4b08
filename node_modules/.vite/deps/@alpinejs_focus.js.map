{"version": 3, "sources": ["../../@alpinejs/focus/dist/module.esm.js"], "sourcesContent": ["// node_modules/tabbable/dist/index.esm.js\nvar candidateSelectors = [\"input\", \"select\", \"textarea\", \"a[href]\", \"button\", \"[tabindex]:not(slot)\", \"audio[controls]\", \"video[controls]\", '[contenteditable]:not([contenteditable=\"false\"])', \"details>summary:first-of-type\", \"details\"];\nvar candidateSelector = /* @__PURE__ */ candidateSelectors.join(\",\");\nvar NoElement = typeof Element === \"undefined\";\nvar matches = NoElement ? function() {\n} : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;\nvar getRootNode = !NoElement && Element.prototype.getRootNode ? function(element) {\n  return element.getRootNode();\n} : function(element) {\n  return element.ownerDocument;\n};\nvar getCandidates = function getCandidates2(el, includeContainer, filter) {\n  var candidates = Array.prototype.slice.apply(el.querySelectorAll(candidateSelector));\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\nvar getCandidatesIteratively = function getCandidatesIteratively2(elements, includeContainer, options) {\n  var candidates = [];\n  var elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    var element = elementsToCheck.shift();\n    if (element.tagName === \"SLOT\") {\n      var assigned = element.assignedElements();\n      var content = assigned.length ? assigned : element.children;\n      var nestedCandidates = getCandidatesIteratively2(content, true, options);\n      if (options.flatten) {\n        candidates.push.apply(candidates, nestedCandidates);\n      } else {\n        candidates.push({\n          scope: element,\n          candidates: nestedCandidates\n        });\n      }\n    } else {\n      var validCandidate = matches.call(element, candidateSelector);\n      if (validCandidate && options.filter(element) && (includeContainer || !elements.includes(element))) {\n        candidates.push(element);\n      }\n      var shadowRoot = element.shadowRoot || // check for an undisclosed shadow\n      typeof options.getShadowRoot === \"function\" && options.getShadowRoot(element);\n      var validShadowRoot = !options.shadowRootFilter || options.shadowRootFilter(element);\n      if (shadowRoot && validShadowRoot) {\n        var _nestedCandidates = getCandidatesIteratively2(shadowRoot === true ? element.children : shadowRoot.children, true, options);\n        if (options.flatten) {\n          candidates.push.apply(candidates, _nestedCandidates);\n        } else {\n          candidates.push({\n            scope: element,\n            candidates: _nestedCandidates\n          });\n        }\n      } else {\n        elementsToCheck.unshift.apply(elementsToCheck, element.children);\n      }\n    }\n  }\n  return candidates;\n};\nvar getTabindex = function getTabindex2(node, isScope) {\n  if (node.tabIndex < 0) {\n    if ((isScope || /^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) || node.isContentEditable) && isNaN(parseInt(node.getAttribute(\"tabindex\"), 10))) {\n      return 0;\n    }\n  }\n  return node.tabIndex;\n};\nvar sortOrderedTabbables = function sortOrderedTabbables2(a, b) {\n  return a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex;\n};\nvar isInput = function isInput2(node) {\n  return node.tagName === \"INPUT\";\n};\nvar isHiddenInput = function isHiddenInput2(node) {\n  return isInput(node) && node.type === \"hidden\";\n};\nvar isDetailsWithSummary = function isDetailsWithSummary2(node) {\n  var r = node.tagName === \"DETAILS\" && Array.prototype.slice.apply(node.children).some(function(child) {\n    return child.tagName === \"SUMMARY\";\n  });\n  return r;\n};\nvar getCheckedRadio = function getCheckedRadio2(nodes, form) {\n  for (var i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\nvar isTabbableRadio = function isTabbableRadio2(node) {\n  if (!node.name) {\n    return true;\n  }\n  var radioScope = node.form || getRootNode(node);\n  var queryRadios = function queryRadios2(name) {\n    return radioScope.querySelectorAll('input[type=\"radio\"][name=\"' + name + '\"]');\n  };\n  var radioSet;\n  if (typeof window !== \"undefined\" && typeof window.CSS !== \"undefined\" && typeof window.CSS.escape === \"function\") {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      console.error(\"Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s\", err.message);\n      return false;\n    }\n  }\n  var checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\nvar isRadio = function isRadio2(node) {\n  return isInput(node) && node.type === \"radio\";\n};\nvar isNonTabbableRadio = function isNonTabbableRadio2(node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\nvar isZeroArea = function isZeroArea2(node) {\n  var _node$getBoundingClie = node.getBoundingClientRect(), width = _node$getBoundingClie.width, height = _node$getBoundingClie.height;\n  return width === 0 && height === 0;\n};\nvar isHidden = function isHidden2(node, _ref) {\n  var displayCheck = _ref.displayCheck, getShadowRoot = _ref.getShadowRoot;\n  if (getComputedStyle(node).visibility === \"hidden\") {\n    return true;\n  }\n  var isDirectSummary = matches.call(node, \"details>summary:first-of-type\");\n  var nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, \"details:not([open]) *\")) {\n    return true;\n  }\n  var nodeRootHost = getRootNode(node).host;\n  var nodeIsAttached = (nodeRootHost === null || nodeRootHost === void 0 ? void 0 : nodeRootHost.ownerDocument.contains(nodeRootHost)) || node.ownerDocument.contains(node);\n  if (!displayCheck || displayCheck === \"full\") {\n    if (typeof getShadowRoot === \"function\") {\n      var originalNode = node;\n      while (node) {\n        var parentElement = node.parentElement;\n        var rootNode = getRootNode(node);\n        if (parentElement && !parentElement.shadowRoot && getShadowRoot(parentElement) === true) {\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          node = rootNode.host;\n        } else {\n          node = parentElement;\n        }\n      }\n      node = originalNode;\n    }\n    if (nodeIsAttached) {\n      return !node.getClientRects().length;\n    }\n  } else if (displayCheck === \"non-zero-area\") {\n    return isZeroArea(node);\n  }\n  return false;\n};\nvar isDisabledFromFieldset = function isDisabledFromFieldset2(node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    var parentNode = node.parentElement;\n    while (parentNode) {\n      if (parentNode.tagName === \"FIELDSET\" && parentNode.disabled) {\n        for (var i = 0; i < parentNode.children.length; i++) {\n          var child = parentNode.children.item(i);\n          if (child.tagName === \"LEGEND\") {\n            return matches.call(parentNode, \"fieldset[disabled] *\") ? true : !child.contains(node);\n          }\n        }\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n  return false;\n};\nvar isNodeMatchingSelectorFocusable = function isNodeMatchingSelectorFocusable2(options, node) {\n  if (node.disabled || isHiddenInput(node) || isHidden(node, options) || // For a details element with a summary, the summary element gets the focus\n  isDetailsWithSummary(node) || isDisabledFromFieldset(node)) {\n    return false;\n  }\n  return true;\n};\nvar isNodeMatchingSelectorTabbable = function isNodeMatchingSelectorTabbable2(options, node) {\n  if (isNonTabbableRadio(node) || getTabindex(node) < 0 || !isNodeMatchingSelectorFocusable(options, node)) {\n    return false;\n  }\n  return true;\n};\nvar isValidShadowRootTabbable = function isValidShadowRootTabbable2(shadowHostNode) {\n  var tabIndex = parseInt(shadowHostNode.getAttribute(\"tabindex\"), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  return false;\n};\nvar sortByOrder = function sortByOrder2(candidates) {\n  var regularTabbables = [];\n  var orderedTabbables = [];\n  candidates.forEach(function(item, i) {\n    var isScope = !!item.scope;\n    var element = isScope ? item.scope : item;\n    var candidateTabindex = getTabindex(element, isScope);\n    var elements = isScope ? sortByOrder2(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope ? regularTabbables.push.apply(regularTabbables, elements) : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item,\n        isScope,\n        content: elements\n      });\n    }\n  });\n  return orderedTabbables.sort(sortOrderedTabbables).reduce(function(acc, sortable) {\n    sortable.isScope ? acc.push.apply(acc, sortable.content) : acc.push(sortable.content);\n    return acc;\n  }, []).concat(regularTabbables);\n};\nvar tabbable = function tabbable2(el, options) {\n  options = options || {};\n  var candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([el], options.includeContainer, {\n      filter: isNodeMatchingSelectorTabbable.bind(null, options),\n      flatten: false,\n      getShadowRoot: options.getShadowRoot,\n      shadowRootFilter: isValidShadowRootTabbable\n    });\n  } else {\n    candidates = getCandidates(el, options.includeContainer, isNodeMatchingSelectorTabbable.bind(null, options));\n  }\n  return sortByOrder(candidates);\n};\nvar focusable = function focusable2(el, options) {\n  options = options || {};\n  var candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([el], options.includeContainer, {\n      filter: isNodeMatchingSelectorFocusable.bind(null, options),\n      flatten: true,\n      getShadowRoot: options.getShadowRoot\n    });\n  } else {\n    candidates = getCandidates(el, options.includeContainer, isNodeMatchingSelectorFocusable.bind(null, options));\n  }\n  return candidates;\n};\nvar isTabbable = function isTabbable2(node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error(\"No node provided\");\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\nvar focusableCandidateSelector = /* @__PURE__ */ candidateSelectors.concat(\"iframe\").join(\",\");\nvar isFocusable = function isFocusable2(node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error(\"No node provided\");\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\n// node_modules/focus-trap/dist/focus-trap.esm.js\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function(sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), true).forEach(function(key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nvar activeFocusTraps = function() {\n  var trapQueue = [];\n  return {\n    activateTrap: function activateTrap(trap) {\n      if (trapQueue.length > 0) {\n        var activeTrap = trapQueue[trapQueue.length - 1];\n        if (activeTrap !== trap) {\n          activeTrap.pause();\n        }\n      }\n      var trapIndex = trapQueue.indexOf(trap);\n      if (trapIndex === -1) {\n        trapQueue.push(trap);\n      } else {\n        trapQueue.splice(trapIndex, 1);\n        trapQueue.push(trap);\n      }\n    },\n    deactivateTrap: function deactivateTrap(trap) {\n      var trapIndex = trapQueue.indexOf(trap);\n      if (trapIndex !== -1) {\n        trapQueue.splice(trapIndex, 1);\n      }\n      if (trapQueue.length > 0) {\n        trapQueue[trapQueue.length - 1].unpause();\n      }\n    }\n  };\n}();\nvar isSelectableInput = function isSelectableInput2(node) {\n  return node.tagName && node.tagName.toLowerCase() === \"input\" && typeof node.select === \"function\";\n};\nvar isEscapeEvent = function isEscapeEvent2(e) {\n  return e.key === \"Escape\" || e.key === \"Esc\" || e.keyCode === 27;\n};\nvar isTabEvent = function isTabEvent2(e) {\n  return e.key === \"Tab\" || e.keyCode === 9;\n};\nvar delay = function delay2(fn) {\n  return setTimeout(fn, 0);\n};\nvar findIndex = function findIndex2(arr, fn) {\n  var idx = -1;\n  arr.every(function(value, i) {\n    if (fn(value)) {\n      idx = i;\n      return false;\n    }\n    return true;\n  });\n  return idx;\n};\nvar valueOrHandler = function valueOrHandler2(value) {\n  for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    params[_key - 1] = arguments[_key];\n  }\n  return typeof value === \"function\" ? value.apply(void 0, params) : value;\n};\nvar getActualTarget = function getActualTarget2(event) {\n  return event.target.shadowRoot && typeof event.composedPath === \"function\" ? event.composedPath()[0] : event.target;\n};\nvar createFocusTrap = function createFocusTrap2(elements, userOptions) {\n  var doc = (userOptions === null || userOptions === void 0 ? void 0 : userOptions.document) || document;\n  var config = _objectSpread2({\n    returnFocusOnDeactivate: true,\n    escapeDeactivates: true,\n    delayInitialFocus: true\n  }, userOptions);\n  var state = {\n    // containers given to createFocusTrap()\n    // @type {Array<HTMLElement>}\n    containers: [],\n    // list of objects identifying tabbable nodes in `containers` in the trap\n    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap\n    //  is active, but the trap should never get to a state where there isn't at least one group\n    //  with at least one tabbable node in it (that would lead to an error condition that would\n    //  result in an error being thrown)\n    // @type {Array<{\n    //   container: HTMLElement,\n    //   tabbableNodes: Array<HTMLElement>, // empty if none\n    //   focusableNodes: Array<HTMLElement>, // empty if none\n    //   firstTabbableNode: HTMLElement|null,\n    //   lastTabbableNode: HTMLElement|null,\n    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined\n    // }>}\n    containerGroups: [],\n    // same order/length as `containers` list\n    // references to objects in `containerGroups`, but only those that actually have\n    //  tabbable nodes in them\n    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__\n    //  the same length\n    tabbableGroups: [],\n    nodeFocusedBeforeActivation: null,\n    mostRecentlyFocusedNode: null,\n    active: false,\n    paused: false,\n    // timer ID for when delayInitialFocus is true and initial focus in this trap\n    //  has been delayed during activation\n    delayInitialFocusTimer: void 0\n  };\n  var trap;\n  var getOption = function getOption2(configOverrideOptions, optionName, configOptionName) {\n    return configOverrideOptions && configOverrideOptions[optionName] !== void 0 ? configOverrideOptions[optionName] : config[configOptionName || optionName];\n  };\n  var findContainerIndex = function findContainerIndex2(element) {\n    return state.containerGroups.findIndex(function(_ref) {\n      var container = _ref.container, tabbableNodes = _ref.tabbableNodes;\n      return container.contains(element) || // fall back to explicit tabbable search which will take into consideration any\n      //  web components if the `tabbableOptions.getShadowRoot` option was used for\n      //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't\n      //  look inside web components even if open)\n      tabbableNodes.find(function(node) {\n        return node === element;\n      });\n    });\n  };\n  var getNodeForOption = function getNodeForOption2(optionName) {\n    var optionValue = config[optionName];\n    if (typeof optionValue === \"function\") {\n      for (var _len2 = arguments.length, params = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        params[_key2 - 1] = arguments[_key2];\n      }\n      optionValue = optionValue.apply(void 0, params);\n    }\n    if (optionValue === true) {\n      optionValue = void 0;\n    }\n    if (!optionValue) {\n      if (optionValue === void 0 || optionValue === false) {\n        return optionValue;\n      }\n      throw new Error(\"`\".concat(optionName, \"` was specified but was not a node, or did not return a node\"));\n    }\n    var node = optionValue;\n    if (typeof optionValue === \"string\") {\n      node = doc.querySelector(optionValue);\n      if (!node) {\n        throw new Error(\"`\".concat(optionName, \"` as selector refers to no known node\"));\n      }\n    }\n    return node;\n  };\n  var getInitialFocusNode = function getInitialFocusNode2() {\n    var node = getNodeForOption(\"initialFocus\");\n    if (node === false) {\n      return false;\n    }\n    if (node === void 0) {\n      if (findContainerIndex(doc.activeElement) >= 0) {\n        node = doc.activeElement;\n      } else {\n        var firstTabbableGroup = state.tabbableGroups[0];\n        var firstTabbableNode = firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n        node = firstTabbableNode || getNodeForOption(\"fallbackFocus\");\n      }\n    }\n    if (!node) {\n      throw new Error(\"Your focus-trap needs to have at least one focusable element\");\n    }\n    return node;\n  };\n  var updateTabbableNodes = function updateTabbableNodes2() {\n    state.containerGroups = state.containers.map(function(container) {\n      var tabbableNodes = tabbable(container, config.tabbableOptions);\n      var focusableNodes = focusable(container, config.tabbableOptions);\n      return {\n        container,\n        tabbableNodes,\n        focusableNodes,\n        firstTabbableNode: tabbableNodes.length > 0 ? tabbableNodes[0] : null,\n        lastTabbableNode: tabbableNodes.length > 0 ? tabbableNodes[tabbableNodes.length - 1] : null,\n        /**\n         * Finds the __tabbable__ node that follows the given node in the specified direction,\n         *  in this container, if any.\n         * @param {HTMLElement} node\n         * @param {boolean} [forward] True if going in forward tab order; false if going\n         *  in reverse.\n         * @returns {HTMLElement|undefined} The next tabbable node, if any.\n         */\n        nextTabbableNode: function nextTabbableNode(node) {\n          var forward = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n          var nodeIdx = focusableNodes.findIndex(function(n) {\n            return n === node;\n          });\n          if (nodeIdx < 0) {\n            return void 0;\n          }\n          if (forward) {\n            return focusableNodes.slice(nodeIdx + 1).find(function(n) {\n              return isTabbable(n, config.tabbableOptions);\n            });\n          }\n          return focusableNodes.slice(0, nodeIdx).reverse().find(function(n) {\n            return isTabbable(n, config.tabbableOptions);\n          });\n        }\n      };\n    });\n    state.tabbableGroups = state.containerGroups.filter(function(group) {\n      return group.tabbableNodes.length > 0;\n    });\n    if (state.tabbableGroups.length <= 0 && !getNodeForOption(\"fallbackFocus\")) {\n      throw new Error(\"Your focus-trap must have at least one container with at least one tabbable node in it at all times\");\n    }\n  };\n  var tryFocus = function tryFocus2(node) {\n    if (node === false) {\n      return;\n    }\n    if (node === doc.activeElement) {\n      return;\n    }\n    if (!node || !node.focus) {\n      tryFocus2(getInitialFocusNode());\n      return;\n    }\n    node.focus({\n      preventScroll: !!config.preventScroll\n    });\n    state.mostRecentlyFocusedNode = node;\n    if (isSelectableInput(node)) {\n      node.select();\n    }\n  };\n  var getReturnFocusNode = function getReturnFocusNode2(previousActiveElement) {\n    var node = getNodeForOption(\"setReturnFocus\", previousActiveElement);\n    return node ? node : node === false ? false : previousActiveElement;\n  };\n  var checkPointerDown = function checkPointerDown2(e) {\n    var target = getActualTarget(e);\n    if (findContainerIndex(target) >= 0) {\n      return;\n    }\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      trap.deactivate({\n        // if, on deactivation, we should return focus to the node originally-focused\n        //  when the trap was activated (or the configured `setReturnFocus` node),\n        //  then assume it's also OK to return focus to the outside node that was\n        //  just clicked, causing deactivation, as long as that node is focusable;\n        //  if it isn't focusable, then return focus to the original node focused\n        //  on activation (or the configured `setReturnFocus` node)\n        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,\n        //  which will result in the outside click setting focus to the node\n        //  that was clicked, whether it's focusable or not; by setting\n        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused\n        //  on activation (or the configured `setReturnFocus` node)\n        returnFocus: config.returnFocusOnDeactivate && !isFocusable(target, config.tabbableOptions)\n      });\n      return;\n    }\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n    e.preventDefault();\n  };\n  var checkFocusIn = function checkFocusIn2(e) {\n    var target = getActualTarget(e);\n    var targetContained = findContainerIndex(target) >= 0;\n    if (targetContained || target instanceof Document) {\n      if (targetContained) {\n        state.mostRecentlyFocusedNode = target;\n      }\n    } else {\n      e.stopImmediatePropagation();\n      tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n    }\n  };\n  var checkTab = function checkTab2(e) {\n    var target = getActualTarget(e);\n    updateTabbableNodes();\n    var destinationNode = null;\n    if (state.tabbableGroups.length > 0) {\n      var containerIndex = findContainerIndex(target);\n      var containerGroup = containerIndex >= 0 ? state.containerGroups[containerIndex] : void 0;\n      if (containerIndex < 0) {\n        if (e.shiftKey) {\n          destinationNode = state.tabbableGroups[state.tabbableGroups.length - 1].lastTabbableNode;\n        } else {\n          destinationNode = state.tabbableGroups[0].firstTabbableNode;\n        }\n      } else if (e.shiftKey) {\n        var startOfGroupIndex = findIndex(state.tabbableGroups, function(_ref2) {\n          var firstTabbableNode = _ref2.firstTabbableNode;\n          return target === firstTabbableNode;\n        });\n        if (startOfGroupIndex < 0 && (containerGroup.container === target || isFocusable(target, config.tabbableOptions) && !isTabbable(target, config.tabbableOptions) && !containerGroup.nextTabbableNode(target, false))) {\n          startOfGroupIndex = containerIndex;\n        }\n        if (startOfGroupIndex >= 0) {\n          var destinationGroupIndex = startOfGroupIndex === 0 ? state.tabbableGroups.length - 1 : startOfGroupIndex - 1;\n          var destinationGroup = state.tabbableGroups[destinationGroupIndex];\n          destinationNode = destinationGroup.lastTabbableNode;\n        }\n      } else {\n        var lastOfGroupIndex = findIndex(state.tabbableGroups, function(_ref3) {\n          var lastTabbableNode = _ref3.lastTabbableNode;\n          return target === lastTabbableNode;\n        });\n        if (lastOfGroupIndex < 0 && (containerGroup.container === target || isFocusable(target, config.tabbableOptions) && !isTabbable(target, config.tabbableOptions) && !containerGroup.nextTabbableNode(target))) {\n          lastOfGroupIndex = containerIndex;\n        }\n        if (lastOfGroupIndex >= 0) {\n          var _destinationGroupIndex = lastOfGroupIndex === state.tabbableGroups.length - 1 ? 0 : lastOfGroupIndex + 1;\n          var _destinationGroup = state.tabbableGroups[_destinationGroupIndex];\n          destinationNode = _destinationGroup.firstTabbableNode;\n        }\n      }\n    } else {\n      destinationNode = getNodeForOption(\"fallbackFocus\");\n    }\n    if (destinationNode) {\n      e.preventDefault();\n      tryFocus(destinationNode);\n    }\n  };\n  var checkKey = function checkKey2(e) {\n    if (isEscapeEvent(e) && valueOrHandler(config.escapeDeactivates, e) !== false) {\n      e.preventDefault();\n      trap.deactivate();\n      return;\n    }\n    if (isTabEvent(e)) {\n      checkTab(e);\n      return;\n    }\n  };\n  var checkClick = function checkClick2(e) {\n    var target = getActualTarget(e);\n    if (findContainerIndex(target) >= 0) {\n      return;\n    }\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      return;\n    }\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n    e.preventDefault();\n    e.stopImmediatePropagation();\n  };\n  var addListeners = function addListeners2() {\n    if (!state.active) {\n      return;\n    }\n    activeFocusTraps.activateTrap(trap);\n    state.delayInitialFocusTimer = config.delayInitialFocus ? delay(function() {\n      tryFocus(getInitialFocusNode());\n    }) : tryFocus(getInitialFocusNode());\n    doc.addEventListener(\"focusin\", checkFocusIn, true);\n    doc.addEventListener(\"mousedown\", checkPointerDown, {\n      capture: true,\n      passive: false\n    });\n    doc.addEventListener(\"touchstart\", checkPointerDown, {\n      capture: true,\n      passive: false\n    });\n    doc.addEventListener(\"click\", checkClick, {\n      capture: true,\n      passive: false\n    });\n    doc.addEventListener(\"keydown\", checkKey, {\n      capture: true,\n      passive: false\n    });\n    return trap;\n  };\n  var removeListeners = function removeListeners2() {\n    if (!state.active) {\n      return;\n    }\n    doc.removeEventListener(\"focusin\", checkFocusIn, true);\n    doc.removeEventListener(\"mousedown\", checkPointerDown, true);\n    doc.removeEventListener(\"touchstart\", checkPointerDown, true);\n    doc.removeEventListener(\"click\", checkClick, true);\n    doc.removeEventListener(\"keydown\", checkKey, true);\n    return trap;\n  };\n  trap = {\n    get active() {\n      return state.active;\n    },\n    get paused() {\n      return state.paused;\n    },\n    activate: function activate(activateOptions) {\n      if (state.active) {\n        return this;\n      }\n      var onActivate = getOption(activateOptions, \"onActivate\");\n      var onPostActivate = getOption(activateOptions, \"onPostActivate\");\n      var checkCanFocusTrap = getOption(activateOptions, \"checkCanFocusTrap\");\n      if (!checkCanFocusTrap) {\n        updateTabbableNodes();\n      }\n      state.active = true;\n      state.paused = false;\n      state.nodeFocusedBeforeActivation = doc.activeElement;\n      if (onActivate) {\n        onActivate();\n      }\n      var finishActivation = function finishActivation2() {\n        if (checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        addListeners();\n        if (onPostActivate) {\n          onPostActivate();\n        }\n      };\n      if (checkCanFocusTrap) {\n        checkCanFocusTrap(state.containers.concat()).then(finishActivation, finishActivation);\n        return this;\n      }\n      finishActivation();\n      return this;\n    },\n    deactivate: function deactivate(deactivateOptions) {\n      if (!state.active) {\n        return this;\n      }\n      var options = _objectSpread2({\n        onDeactivate: config.onDeactivate,\n        onPostDeactivate: config.onPostDeactivate,\n        checkCanReturnFocus: config.checkCanReturnFocus\n      }, deactivateOptions);\n      clearTimeout(state.delayInitialFocusTimer);\n      state.delayInitialFocusTimer = void 0;\n      removeListeners();\n      state.active = false;\n      state.paused = false;\n      activeFocusTraps.deactivateTrap(trap);\n      var onDeactivate = getOption(options, \"onDeactivate\");\n      var onPostDeactivate = getOption(options, \"onPostDeactivate\");\n      var checkCanReturnFocus = getOption(options, \"checkCanReturnFocus\");\n      var returnFocus = getOption(options, \"returnFocus\", \"returnFocusOnDeactivate\");\n      if (onDeactivate) {\n        onDeactivate();\n      }\n      var finishDeactivation = function finishDeactivation2() {\n        delay(function() {\n          if (returnFocus) {\n            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n          }\n          if (onPostDeactivate) {\n            onPostDeactivate();\n          }\n        });\n      };\n      if (returnFocus && checkCanReturnFocus) {\n        checkCanReturnFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation)).then(finishDeactivation, finishDeactivation);\n        return this;\n      }\n      finishDeactivation();\n      return this;\n    },\n    pause: function pause() {\n      if (state.paused || !state.active) {\n        return this;\n      }\n      state.paused = true;\n      removeListeners();\n      return this;\n    },\n    unpause: function unpause() {\n      if (!state.paused || !state.active) {\n        return this;\n      }\n      state.paused = false;\n      updateTabbableNodes();\n      addListeners();\n      return this;\n    },\n    updateContainerElements: function updateContainerElements(containerElements) {\n      var elementsAsArray = [].concat(containerElements).filter(Boolean);\n      state.containers = elementsAsArray.map(function(element) {\n        return typeof element === \"string\" ? doc.querySelector(element) : element;\n      });\n      if (state.active) {\n        updateTabbableNodes();\n      }\n      return this;\n    }\n  };\n  trap.updateContainerElements(elements);\n  return trap;\n};\n\n// packages/focus/src/index.js\nfunction src_default(Alpine) {\n  let lastFocused;\n  let currentFocused;\n  window.addEventListener(\"focusin\", () => {\n    lastFocused = currentFocused;\n    currentFocused = document.activeElement;\n  });\n  Alpine.magic(\"focus\", (el) => {\n    let within = el;\n    return {\n      __noscroll: false,\n      __wrapAround: false,\n      within(el2) {\n        within = el2;\n        return this;\n      },\n      withoutScrolling() {\n        this.__noscroll = true;\n        return this;\n      },\n      noscroll() {\n        this.__noscroll = true;\n        return this;\n      },\n      withWrapAround() {\n        this.__wrapAround = true;\n        return this;\n      },\n      wrap() {\n        return this.withWrapAround();\n      },\n      focusable(el2) {\n        return isFocusable(el2);\n      },\n      previouslyFocused() {\n        return lastFocused;\n      },\n      lastFocused() {\n        return lastFocused;\n      },\n      focused() {\n        return currentFocused;\n      },\n      focusables() {\n        if (Array.isArray(within))\n          return within;\n        return focusable(within, { displayCheck: \"none\" });\n      },\n      all() {\n        return this.focusables();\n      },\n      isFirst(el2) {\n        let els = this.all();\n        return els[0] && els[0].isSameNode(el2);\n      },\n      isLast(el2) {\n        let els = this.all();\n        return els.length && els.slice(-1)[0].isSameNode(el2);\n      },\n      getFirst() {\n        return this.all()[0];\n      },\n      getLast() {\n        return this.all().slice(-1)[0];\n      },\n      getNext() {\n        let list = this.all();\n        let current = document.activeElement;\n        if (list.indexOf(current) === -1)\n          return;\n        if (this.__wrapAround && list.indexOf(current) === list.length - 1) {\n          return list[0];\n        }\n        return list[list.indexOf(current) + 1];\n      },\n      getPrevious() {\n        let list = this.all();\n        let current = document.activeElement;\n        if (list.indexOf(current) === -1)\n          return;\n        if (this.__wrapAround && list.indexOf(current) === 0) {\n          return list.slice(-1)[0];\n        }\n        return list[list.indexOf(current) - 1];\n      },\n      first() {\n        this.focus(this.getFirst());\n      },\n      last() {\n        this.focus(this.getLast());\n      },\n      next() {\n        this.focus(this.getNext());\n      },\n      previous() {\n        this.focus(this.getPrevious());\n      },\n      prev() {\n        return this.previous();\n      },\n      focus(el2) {\n        if (!el2)\n          return;\n        setTimeout(() => {\n          if (!el2.hasAttribute(\"tabindex\"))\n            el2.setAttribute(\"tabindex\", \"0\");\n          el2.focus({ preventScroll: this.__noscroll });\n        });\n      }\n    };\n  });\n  Alpine.directive(\"trap\", Alpine.skipDuringClone(\n    (el, { expression, modifiers }, { effect, evaluateLater, cleanup }) => {\n      let evaluator = evaluateLater(expression);\n      let oldValue = false;\n      let options = {\n        escapeDeactivates: false,\n        allowOutsideClick: true,\n        fallbackFocus: () => el\n      };\n      if (modifiers.includes(\"noautofocus\")) {\n        options.initialFocus = false;\n      } else {\n        let autofocusEl = el.querySelector(\"[autofocus]\");\n        if (autofocusEl)\n          options.initialFocus = autofocusEl;\n      }\n      let trap = createFocusTrap(el, options);\n      let undoInert = () => {\n      };\n      let undoDisableScrolling = () => {\n      };\n      const releaseFocus = () => {\n        undoInert();\n        undoInert = () => {\n        };\n        undoDisableScrolling();\n        undoDisableScrolling = () => {\n        };\n        trap.deactivate({\n          returnFocus: !modifiers.includes(\"noreturn\")\n        });\n      };\n      effect(() => evaluator((value) => {\n        if (oldValue === value)\n          return;\n        if (value && !oldValue) {\n          if (modifiers.includes(\"noscroll\"))\n            undoDisableScrolling = disableScrolling();\n          if (modifiers.includes(\"inert\"))\n            undoInert = setInert(el);\n          setTimeout(() => {\n            trap.activate();\n          }, 15);\n        }\n        if (!value && oldValue) {\n          releaseFocus();\n        }\n        oldValue = !!value;\n      }));\n      cleanup(releaseFocus);\n    },\n    // When cloning, we only want to add aria-hidden attributes to the\n    // DOM and not try to actually trap, as trapping can mess with the\n    // live DOM and isn't just isolated to the cloned DOM.\n    (el, { expression, modifiers }, { evaluate }) => {\n      if (modifiers.includes(\"inert\") && evaluate(expression))\n        setInert(el);\n    }\n  ));\n}\nfunction setInert(el) {\n  let undos = [];\n  crawlSiblingsUp(el, (sibling) => {\n    let cache = sibling.hasAttribute(\"aria-hidden\");\n    sibling.setAttribute(\"aria-hidden\", \"true\");\n    undos.push(() => cache || sibling.removeAttribute(\"aria-hidden\"));\n  });\n  return () => {\n    while (undos.length)\n      undos.pop()();\n  };\n}\nfunction crawlSiblingsUp(el, callback) {\n  if (el.isSameNode(document.body) || !el.parentNode)\n    return;\n  Array.from(el.parentNode.children).forEach((sibling) => {\n    if (sibling.isSameNode(el)) {\n      crawlSiblingsUp(el.parentNode, callback);\n    } else {\n      callback(sibling);\n    }\n  });\n}\nfunction disableScrolling() {\n  let overflow = document.documentElement.style.overflow;\n  let paddingRight = document.documentElement.style.paddingRight;\n  let scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;\n  document.documentElement.style.overflow = \"hidden\";\n  document.documentElement.style.paddingRight = `${scrollbarWidth}px`;\n  return () => {\n    document.documentElement.style.overflow = overflow;\n    document.documentElement.style.paddingRight = paddingRight;\n  };\n}\n\n// packages/focus/builds/module.js\nvar module_default = src_default;\nexport {\n  module_default as default,\n  src_default as focus\n};\n/*! Bundled license information:\n\ntabbable/dist/index.esm.js:\n  (*!\n  * tabbable 5.3.3\n  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE\n  *)\n\nfocus-trap/dist/focus-trap.esm.js:\n  (*!\n  * focus-trap 6.9.4\n  * @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE\n  *)\n*/\n"], "mappings": ";AACA,IAAI,qBAAqB,CAAC,SAAS,UAAU,YAAY,WAAW,UAAU,wBAAwB,mBAAmB,mBAAmB,oDAAoD,iCAAiC,SAAS;AAC1O,IAAI,oBAAoC,mBAAmB,KAAK,GAAG;AACnE,IAAI,YAAY,OAAO,YAAY;AACnC,IAAI,UAAU,YAAY,WAAW;AACrC,IAAI,QAAQ,UAAU,WAAW,QAAQ,UAAU,qBAAqB,QAAQ,UAAU;AAC1F,IAAI,cAAc,CAAC,aAAa,QAAQ,UAAU,cAAc,SAAS,SAAS;AAChF,SAAO,QAAQ,YAAY;AAC7B,IAAI,SAAS,SAAS;AACpB,SAAO,QAAQ;AACjB;AACA,IAAI,gBAAgB,SAAS,eAAe,IAAI,kBAAkB,QAAQ;AACxE,MAAI,aAAa,MAAM,UAAU,MAAM,MAAM,GAAG,iBAAiB,iBAAiB,CAAC;AACnF,MAAI,oBAAoB,QAAQ,KAAK,IAAI,iBAAiB,GAAG;AAC3D,eAAW,QAAQ,EAAE;AAAA,EACvB;AACA,eAAa,WAAW,OAAO,MAAM;AACrC,SAAO;AACT;AACA,IAAI,2BAA2B,SAAS,0BAA0B,UAAU,kBAAkB,SAAS;AACrG,MAAI,aAAa,CAAC;AAClB,MAAI,kBAAkB,MAAM,KAAK,QAAQ;AACzC,SAAO,gBAAgB,QAAQ;AAC7B,QAAI,UAAU,gBAAgB,MAAM;AACpC,QAAI,QAAQ,YAAY,QAAQ;AAC9B,UAAI,WAAW,QAAQ,iBAAiB;AACxC,UAAI,UAAU,SAAS,SAAS,WAAW,QAAQ;AACnD,UAAI,mBAAmB,0BAA0B,SAAS,MAAM,OAAO;AACvE,UAAI,QAAQ,SAAS;AACnB,mBAAW,KAAK,MAAM,YAAY,gBAAgB;AAAA,MACpD,OAAO;AACL,mBAAW,KAAK;AAAA,UACd,OAAO;AAAA,UACP,YAAY;AAAA,QACd,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,UAAI,iBAAiB,QAAQ,KAAK,SAAS,iBAAiB;AAC5D,UAAI,kBAAkB,QAAQ,OAAO,OAAO,MAAM,oBAAoB,CAAC,SAAS,SAAS,OAAO,IAAI;AAClG,mBAAW,KAAK,OAAO;AAAA,MACzB;AACA,UAAI,aAAa,QAAQ;AAAA,MACzB,OAAO,QAAQ,kBAAkB,cAAc,QAAQ,cAAc,OAAO;AAC5E,UAAI,kBAAkB,CAAC,QAAQ,oBAAoB,QAAQ,iBAAiB,OAAO;AACnF,UAAI,cAAc,iBAAiB;AACjC,YAAI,oBAAoB,0BAA0B,eAAe,OAAO,QAAQ,WAAW,WAAW,UAAU,MAAM,OAAO;AAC7H,YAAI,QAAQ,SAAS;AACnB,qBAAW,KAAK,MAAM,YAAY,iBAAiB;AAAA,QACrD,OAAO;AACL,qBAAW,KAAK;AAAA,YACd,OAAO;AAAA,YACP,YAAY;AAAA,UACd,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,wBAAgB,QAAQ,MAAM,iBAAiB,QAAQ,QAAQ;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,cAAc,SAAS,aAAa,MAAM,SAAS;AACrD,MAAI,KAAK,WAAW,GAAG;AACrB,SAAK,WAAW,0BAA0B,KAAK,KAAK,OAAO,KAAK,KAAK,sBAAsB,MAAM,SAAS,KAAK,aAAa,UAAU,GAAG,EAAE,CAAC,GAAG;AAC7I,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,KAAK;AACd;AACA,IAAI,uBAAuB,SAAS,sBAAsB,GAAG,GAAG;AAC9D,SAAO,EAAE,aAAa,EAAE,WAAW,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,WAAW,EAAE;AACxF;AACA,IAAI,UAAU,SAAS,SAAS,MAAM;AACpC,SAAO,KAAK,YAAY;AAC1B;AACA,IAAI,gBAAgB,SAAS,eAAe,MAAM;AAChD,SAAO,QAAQ,IAAI,KAAK,KAAK,SAAS;AACxC;AACA,IAAI,uBAAuB,SAAS,sBAAsB,MAAM;AAC9D,MAAI,IAAI,KAAK,YAAY,aAAa,MAAM,UAAU,MAAM,MAAM,KAAK,QAAQ,EAAE,KAAK,SAAS,OAAO;AACpG,WAAO,MAAM,YAAY;AAAA,EAC3B,CAAC;AACD,SAAO;AACT;AACA,IAAI,kBAAkB,SAAS,iBAAiB,OAAO,MAAM;AAC3D,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,MAAM,CAAC,EAAE,WAAW,MAAM,CAAC,EAAE,SAAS,MAAM;AAC9C,aAAO,MAAM,CAAC;AAAA,IAChB;AAAA,EACF;AACF;AACA,IAAI,kBAAkB,SAAS,iBAAiB,MAAM;AACpD,MAAI,CAAC,KAAK,MAAM;AACd,WAAO;AAAA,EACT;AACA,MAAI,aAAa,KAAK,QAAQ,YAAY,IAAI;AAC9C,MAAI,cAAc,SAAS,aAAa,MAAM;AAC5C,WAAO,WAAW,iBAAiB,+BAA+B,OAAO,IAAI;AAAA,EAC/E;AACA,MAAI;AACJ,MAAI,OAAO,WAAW,eAAe,OAAO,OAAO,QAAQ,eAAe,OAAO,OAAO,IAAI,WAAW,YAAY;AACjH,eAAW,YAAY,OAAO,IAAI,OAAO,KAAK,IAAI,CAAC;AAAA,EACrD,OAAO;AACL,QAAI;AACF,iBAAW,YAAY,KAAK,IAAI;AAAA,IAClC,SAAS,KAAK;AACZ,cAAQ,MAAM,4IAA4I,IAAI,OAAO;AACrK,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,UAAU,gBAAgB,UAAU,KAAK,IAAI;AACjD,SAAO,CAAC,WAAW,YAAY;AACjC;AACA,IAAI,UAAU,SAAS,SAAS,MAAM;AACpC,SAAO,QAAQ,IAAI,KAAK,KAAK,SAAS;AACxC;AACA,IAAI,qBAAqB,SAAS,oBAAoB,MAAM;AAC1D,SAAO,QAAQ,IAAI,KAAK,CAAC,gBAAgB,IAAI;AAC/C;AACA,IAAI,aAAa,SAAS,YAAY,MAAM;AAC1C,MAAI,wBAAwB,KAAK,sBAAsB,GAAG,QAAQ,sBAAsB,OAAO,SAAS,sBAAsB;AAC9H,SAAO,UAAU,KAAK,WAAW;AACnC;AACA,IAAI,WAAW,SAAS,UAAU,MAAM,MAAM;AAC5C,MAAI,eAAe,KAAK,cAAc,gBAAgB,KAAK;AAC3D,MAAI,iBAAiB,IAAI,EAAE,eAAe,UAAU;AAClD,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB,QAAQ,KAAK,MAAM,+BAA+B;AACxE,MAAI,mBAAmB,kBAAkB,KAAK,gBAAgB;AAC9D,MAAI,QAAQ,KAAK,kBAAkB,uBAAuB,GAAG;AAC3D,WAAO;AAAA,EACT;AACA,MAAI,eAAe,YAAY,IAAI,EAAE;AACrC,MAAI,kBAAkB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,cAAc,SAAS,YAAY,MAAM,KAAK,cAAc,SAAS,IAAI;AACxK,MAAI,CAAC,gBAAgB,iBAAiB,QAAQ;AAC5C,QAAI,OAAO,kBAAkB,YAAY;AACvC,UAAI,eAAe;AACnB,aAAO,MAAM;AACX,YAAI,gBAAgB,KAAK;AACzB,YAAI,WAAW,YAAY,IAAI;AAC/B,YAAI,iBAAiB,CAAC,cAAc,cAAc,cAAc,aAAa,MAAM,MAAM;AACvF,iBAAO,WAAW,IAAI;AAAA,QACxB,WAAW,KAAK,cAAc;AAC5B,iBAAO,KAAK;AAAA,QACd,WAAW,CAAC,iBAAiB,aAAa,KAAK,eAAe;AAC5D,iBAAO,SAAS;AAAA,QAClB,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,QAAI,gBAAgB;AAClB,aAAO,CAAC,KAAK,eAAe,EAAE;AAAA,IAChC;AAAA,EACF,WAAW,iBAAiB,iBAAiB;AAC3C,WAAO,WAAW,IAAI;AAAA,EACxB;AACA,SAAO;AACT;AACA,IAAI,yBAAyB,SAAS,wBAAwB,MAAM;AAClE,MAAI,mCAAmC,KAAK,KAAK,OAAO,GAAG;AACzD,QAAI,aAAa,KAAK;AACtB,WAAO,YAAY;AACjB,UAAI,WAAW,YAAY,cAAc,WAAW,UAAU;AAC5D,iBAAS,IAAI,GAAG,IAAI,WAAW,SAAS,QAAQ,KAAK;AACnD,cAAI,QAAQ,WAAW,SAAS,KAAK,CAAC;AACtC,cAAI,MAAM,YAAY,UAAU;AAC9B,mBAAO,QAAQ,KAAK,YAAY,sBAAsB,IAAI,OAAO,CAAC,MAAM,SAAS,IAAI;AAAA,UACvF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,mBAAa,WAAW;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,kCAAkC,SAAS,iCAAiC,SAAS,MAAM;AAC7F,MAAI,KAAK,YAAY,cAAc,IAAI,KAAK,SAAS,MAAM,OAAO;AAAA,EAClE,qBAAqB,IAAI,KAAK,uBAAuB,IAAI,GAAG;AAC1D,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAI,iCAAiC,SAAS,gCAAgC,SAAS,MAAM;AAC3F,MAAI,mBAAmB,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,CAAC,gCAAgC,SAAS,IAAI,GAAG;AACxG,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAI,4BAA4B,SAAS,2BAA2B,gBAAgB;AAClF,MAAI,WAAW,SAAS,eAAe,aAAa,UAAU,GAAG,EAAE;AACnE,MAAI,MAAM,QAAQ,KAAK,YAAY,GAAG;AACpC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAI,cAAc,SAAS,aAAa,YAAY;AAClD,MAAI,mBAAmB,CAAC;AACxB,MAAI,mBAAmB,CAAC;AACxB,aAAW,QAAQ,SAAS,MAAM,GAAG;AACnC,QAAI,UAAU,CAAC,CAAC,KAAK;AACrB,QAAI,UAAU,UAAU,KAAK,QAAQ;AACrC,QAAI,oBAAoB,YAAY,SAAS,OAAO;AACpD,QAAI,WAAW,UAAU,aAAa,KAAK,UAAU,IAAI;AACzD,QAAI,sBAAsB,GAAG;AAC3B,gBAAU,iBAAiB,KAAK,MAAM,kBAAkB,QAAQ,IAAI,iBAAiB,KAAK,OAAO;AAAA,IACnG,OAAO;AACL,uBAAiB,KAAK;AAAA,QACpB,eAAe;AAAA,QACf,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO,iBAAiB,KAAK,oBAAoB,EAAE,OAAO,SAAS,KAAK,UAAU;AAChF,aAAS,UAAU,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO,IAAI,IAAI,KAAK,SAAS,OAAO;AACpF,WAAO;AAAA,EACT,GAAG,CAAC,CAAC,EAAE,OAAO,gBAAgB;AAChC;AACA,IAAI,WAAW,SAAS,UAAU,IAAI,SAAS;AAC7C,YAAU,WAAW,CAAC;AACtB,MAAI;AACJ,MAAI,QAAQ,eAAe;AACzB,iBAAa,yBAAyB,CAAC,EAAE,GAAG,QAAQ,kBAAkB;AAAA,MACpE,QAAQ,+BAA+B,KAAK,MAAM,OAAO;AAAA,MACzD,SAAS;AAAA,MACT,eAAe,QAAQ;AAAA,MACvB,kBAAkB;AAAA,IACpB,CAAC;AAAA,EACH,OAAO;AACL,iBAAa,cAAc,IAAI,QAAQ,kBAAkB,+BAA+B,KAAK,MAAM,OAAO,CAAC;AAAA,EAC7G;AACA,SAAO,YAAY,UAAU;AAC/B;AACA,IAAI,YAAY,SAAS,WAAW,IAAI,SAAS;AAC/C,YAAU,WAAW,CAAC;AACtB,MAAI;AACJ,MAAI,QAAQ,eAAe;AACzB,iBAAa,yBAAyB,CAAC,EAAE,GAAG,QAAQ,kBAAkB;AAAA,MACpE,QAAQ,gCAAgC,KAAK,MAAM,OAAO;AAAA,MAC1D,SAAS;AAAA,MACT,eAAe,QAAQ;AAAA,IACzB,CAAC;AAAA,EACH,OAAO;AACL,iBAAa,cAAc,IAAI,QAAQ,kBAAkB,gCAAgC,KAAK,MAAM,OAAO,CAAC;AAAA,EAC9G;AACA,SAAO;AACT;AACA,IAAI,aAAa,SAAS,YAAY,MAAM,SAAS;AACnD,YAAU,WAAW,CAAC;AACtB,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,kBAAkB;AAAA,EACpC;AACA,MAAI,QAAQ,KAAK,MAAM,iBAAiB,MAAM,OAAO;AACnD,WAAO;AAAA,EACT;AACA,SAAO,+BAA+B,SAAS,IAAI;AACrD;AACA,IAAI,6BAA6C,mBAAmB,OAAO,QAAQ,EAAE,KAAK,GAAG;AAC7F,IAAI,cAAc,SAAS,aAAa,MAAM,SAAS;AACrD,YAAU,WAAW,CAAC;AACtB,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,kBAAkB;AAAA,EACpC;AACA,MAAI,QAAQ,KAAK,MAAM,0BAA0B,MAAM,OAAO;AAC5D,WAAO;AAAA,EACT;AACA,SAAO,gCAAgC,SAAS,IAAI;AACtD;AAGA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,uBAAmB,UAAU,QAAQ,OAAO,SAAS,KAAK;AACxD,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EACpC;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AACpD,QAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAS,KAAK;AAC1D,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAC1C,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAS,KAAK;AAChK,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IACjF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;AACA,IAAI,mBAAmB,4BAAW;AAChC,MAAI,YAAY,CAAC;AACjB,SAAO;AAAA,IACL,cAAc,SAAS,aAAa,MAAM;AACxC,UAAI,UAAU,SAAS,GAAG;AACxB,YAAI,aAAa,UAAU,UAAU,SAAS,CAAC;AAC/C,YAAI,eAAe,MAAM;AACvB,qBAAW,MAAM;AAAA,QACnB;AAAA,MACF;AACA,UAAI,YAAY,UAAU,QAAQ,IAAI;AACtC,UAAI,cAAc,IAAI;AACpB,kBAAU,KAAK,IAAI;AAAA,MACrB,OAAO;AACL,kBAAU,OAAO,WAAW,CAAC;AAC7B,kBAAU,KAAK,IAAI;AAAA,MACrB;AAAA,IACF;AAAA,IACA,gBAAgB,SAAS,eAAe,MAAM;AAC5C,UAAI,YAAY,UAAU,QAAQ,IAAI;AACtC,UAAI,cAAc,IAAI;AACpB,kBAAU,OAAO,WAAW,CAAC;AAAA,MAC/B;AACA,UAAI,UAAU,SAAS,GAAG;AACxB,kBAAU,UAAU,SAAS,CAAC,EAAE,QAAQ;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AACF,GAAE;AACF,IAAI,oBAAoB,SAAS,mBAAmB,MAAM;AACxD,SAAO,KAAK,WAAW,KAAK,QAAQ,YAAY,MAAM,WAAW,OAAO,KAAK,WAAW;AAC1F;AACA,IAAI,gBAAgB,SAAS,eAAe,GAAG;AAC7C,SAAO,EAAE,QAAQ,YAAY,EAAE,QAAQ,SAAS,EAAE,YAAY;AAChE;AACA,IAAI,aAAa,SAAS,YAAY,GAAG;AACvC,SAAO,EAAE,QAAQ,SAAS,EAAE,YAAY;AAC1C;AACA,IAAI,QAAQ,SAAS,OAAO,IAAI;AAC9B,SAAO,WAAW,IAAI,CAAC;AACzB;AACA,IAAI,YAAY,SAAS,WAAW,KAAK,IAAI;AAC3C,MAAI,MAAM;AACV,MAAI,MAAM,SAAS,OAAO,GAAG;AAC3B,QAAI,GAAG,KAAK,GAAG;AACb,YAAM;AACN,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,CAAC;AACD,SAAO;AACT;AACA,IAAI,iBAAiB,SAAS,gBAAgB,OAAO;AACnD,WAAS,OAAO,UAAU,QAAQ,SAAS,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC5G,WAAO,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACnC;AACA,SAAO,OAAO,UAAU,aAAa,MAAM,MAAM,QAAQ,MAAM,IAAI;AACrE;AACA,IAAI,kBAAkB,SAAS,iBAAiB,OAAO;AACrD,SAAO,MAAM,OAAO,cAAc,OAAO,MAAM,iBAAiB,aAAa,MAAM,aAAa,EAAE,CAAC,IAAI,MAAM;AAC/G;AACA,IAAI,kBAAkB,SAAS,iBAAiB,UAAU,aAAa;AACrE,MAAI,OAAO,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,aAAa;AAC9F,MAAI,SAAS,eAAe;AAAA,IAC1B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,EACrB,GAAG,WAAW;AACd,MAAI,QAAQ;AAAA;AAAA;AAAA,IAGV,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcb,iBAAiB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMlB,gBAAgB,CAAC;AAAA,IACjB,6BAA6B;AAAA,IAC7B,yBAAyB;AAAA,IACzB,QAAQ;AAAA,IACR,QAAQ;AAAA;AAAA;AAAA,IAGR,wBAAwB;AAAA,EAC1B;AACA,MAAI;AACJ,MAAI,YAAY,SAAS,WAAW,uBAAuB,YAAY,kBAAkB;AACvF,WAAO,yBAAyB,sBAAsB,UAAU,MAAM,SAAS,sBAAsB,UAAU,IAAI,OAAO,oBAAoB,UAAU;AAAA,EAC1J;AACA,MAAI,qBAAqB,SAAS,oBAAoB,SAAS;AAC7D,WAAO,MAAM,gBAAgB,UAAU,SAAS,MAAM;AACpD,UAAI,YAAY,KAAK,WAAW,gBAAgB,KAAK;AACrD,aAAO,UAAU,SAAS,OAAO;AAAA;AAAA;AAAA;AAAA,MAIjC,cAAc,KAAK,SAAS,MAAM;AAChC,eAAO,SAAS;AAAA,MAClB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,MAAI,mBAAmB,SAAS,kBAAkB,YAAY;AAC5D,QAAI,cAAc,OAAO,UAAU;AACnC,QAAI,OAAO,gBAAgB,YAAY;AACrC,eAAS,QAAQ,UAAU,QAAQ,SAAS,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACnH,eAAO,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,MACrC;AACA,oBAAc,YAAY,MAAM,QAAQ,MAAM;AAAA,IAChD;AACA,QAAI,gBAAgB,MAAM;AACxB,oBAAc;AAAA,IAChB;AACA,QAAI,CAAC,aAAa;AAChB,UAAI,gBAAgB,UAAU,gBAAgB,OAAO;AACnD,eAAO;AAAA,MACT;AACA,YAAM,IAAI,MAAM,IAAI,OAAO,YAAY,8DAA8D,CAAC;AAAA,IACxG;AACA,QAAI,OAAO;AACX,QAAI,OAAO,gBAAgB,UAAU;AACnC,aAAO,IAAI,cAAc,WAAW;AACpC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,IAAI,OAAO,YAAY,uCAAuC,CAAC;AAAA,MACjF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,sBAAsB,SAAS,uBAAuB;AACxD,QAAI,OAAO,iBAAiB,cAAc;AAC1C,QAAI,SAAS,OAAO;AAClB,aAAO;AAAA,IACT;AACA,QAAI,SAAS,QAAQ;AACnB,UAAI,mBAAmB,IAAI,aAAa,KAAK,GAAG;AAC9C,eAAO,IAAI;AAAA,MACb,OAAO;AACL,YAAI,qBAAqB,MAAM,eAAe,CAAC;AAC/C,YAAI,oBAAoB,sBAAsB,mBAAmB;AACjE,eAAO,qBAAqB,iBAAiB,eAAe;AAAA,MAC9D;AAAA,IACF;AACA,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,8DAA8D;AAAA,IAChF;AACA,WAAO;AAAA,EACT;AACA,MAAI,sBAAsB,SAAS,uBAAuB;AACxD,UAAM,kBAAkB,MAAM,WAAW,IAAI,SAAS,WAAW;AAC/D,UAAI,gBAAgB,SAAS,WAAW,OAAO,eAAe;AAC9D,UAAI,iBAAiB,UAAU,WAAW,OAAO,eAAe;AAChE,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,mBAAmB,cAAc,SAAS,IAAI,cAAc,CAAC,IAAI;AAAA,QACjE,kBAAkB,cAAc,SAAS,IAAI,cAAc,cAAc,SAAS,CAAC,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASvF,kBAAkB,SAAS,iBAAiB,MAAM;AAChD,cAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI;AAC/E,cAAI,UAAU,eAAe,UAAU,SAAS,GAAG;AACjD,mBAAO,MAAM;AAAA,UACf,CAAC;AACD,cAAI,UAAU,GAAG;AACf,mBAAO;AAAA,UACT;AACA,cAAI,SAAS;AACX,mBAAO,eAAe,MAAM,UAAU,CAAC,EAAE,KAAK,SAAS,GAAG;AACxD,qBAAO,WAAW,GAAG,OAAO,eAAe;AAAA,YAC7C,CAAC;AAAA,UACH;AACA,iBAAO,eAAe,MAAM,GAAG,OAAO,EAAE,QAAQ,EAAE,KAAK,SAAS,GAAG;AACjE,mBAAO,WAAW,GAAG,OAAO,eAAe;AAAA,UAC7C,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,iBAAiB,MAAM,gBAAgB,OAAO,SAAS,OAAO;AAClE,aAAO,MAAM,cAAc,SAAS;AAAA,IACtC,CAAC;AACD,QAAI,MAAM,eAAe,UAAU,KAAK,CAAC,iBAAiB,eAAe,GAAG;AAC1E,YAAM,IAAI,MAAM,qGAAqG;AAAA,IACvH;AAAA,EACF;AACA,MAAI,WAAW,SAAS,UAAU,MAAM;AACtC,QAAI,SAAS,OAAO;AAClB;AAAA,IACF;AACA,QAAI,SAAS,IAAI,eAAe;AAC9B;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,CAAC,KAAK,OAAO;AACxB,gBAAU,oBAAoB,CAAC;AAC/B;AAAA,IACF;AACA,SAAK,MAAM;AAAA,MACT,eAAe,CAAC,CAAC,OAAO;AAAA,IAC1B,CAAC;AACD,UAAM,0BAA0B;AAChC,QAAI,kBAAkB,IAAI,GAAG;AAC3B,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AACA,MAAI,qBAAqB,SAAS,oBAAoB,uBAAuB;AAC3E,QAAI,OAAO,iBAAiB,kBAAkB,qBAAqB;AACnE,WAAO,OAAO,OAAO,SAAS,QAAQ,QAAQ;AAAA,EAChD;AACA,MAAI,mBAAmB,SAAS,kBAAkB,GAAG;AACnD,QAAI,SAAS,gBAAgB,CAAC;AAC9B,QAAI,mBAAmB,MAAM,KAAK,GAAG;AACnC;AAAA,IACF;AACA,QAAI,eAAe,OAAO,yBAAyB,CAAC,GAAG;AACrD,WAAK,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAYd,aAAa,OAAO,2BAA2B,CAAC,YAAY,QAAQ,OAAO,eAAe;AAAA,MAC5F,CAAC;AACD;AAAA,IACF;AACA,QAAI,eAAe,OAAO,mBAAmB,CAAC,GAAG;AAC/C;AAAA,IACF;AACA,MAAE,eAAe;AAAA,EACnB;AACA,MAAI,eAAe,SAAS,cAAc,GAAG;AAC3C,QAAI,SAAS,gBAAgB,CAAC;AAC9B,QAAI,kBAAkB,mBAAmB,MAAM,KAAK;AACpD,QAAI,mBAAmB,kBAAkB,UAAU;AACjD,UAAI,iBAAiB;AACnB,cAAM,0BAA0B;AAAA,MAClC;AAAA,IACF,OAAO;AACL,QAAE,yBAAyB;AAC3B,eAAS,MAAM,2BAA2B,oBAAoB,CAAC;AAAA,IACjE;AAAA,EACF;AACA,MAAI,WAAW,SAAS,UAAU,GAAG;AACnC,QAAI,SAAS,gBAAgB,CAAC;AAC9B,wBAAoB;AACpB,QAAI,kBAAkB;AACtB,QAAI,MAAM,eAAe,SAAS,GAAG;AACnC,UAAI,iBAAiB,mBAAmB,MAAM;AAC9C,UAAI,iBAAiB,kBAAkB,IAAI,MAAM,gBAAgB,cAAc,IAAI;AACnF,UAAI,iBAAiB,GAAG;AACtB,YAAI,EAAE,UAAU;AACd,4BAAkB,MAAM,eAAe,MAAM,eAAe,SAAS,CAAC,EAAE;AAAA,QAC1E,OAAO;AACL,4BAAkB,MAAM,eAAe,CAAC,EAAE;AAAA,QAC5C;AAAA,MACF,WAAW,EAAE,UAAU;AACrB,YAAI,oBAAoB,UAAU,MAAM,gBAAgB,SAAS,OAAO;AACtE,cAAI,oBAAoB,MAAM;AAC9B,iBAAO,WAAW;AAAA,QACpB,CAAC;AACD,YAAI,oBAAoB,MAAM,eAAe,cAAc,UAAU,YAAY,QAAQ,OAAO,eAAe,KAAK,CAAC,WAAW,QAAQ,OAAO,eAAe,KAAK,CAAC,eAAe,iBAAiB,QAAQ,KAAK,IAAI;AACnN,8BAAoB;AAAA,QACtB;AACA,YAAI,qBAAqB,GAAG;AAC1B,cAAI,wBAAwB,sBAAsB,IAAI,MAAM,eAAe,SAAS,IAAI,oBAAoB;AAC5G,cAAI,mBAAmB,MAAM,eAAe,qBAAqB;AACjE,4BAAkB,iBAAiB;AAAA,QACrC;AAAA,MACF,OAAO;AACL,YAAI,mBAAmB,UAAU,MAAM,gBAAgB,SAAS,OAAO;AACrE,cAAI,mBAAmB,MAAM;AAC7B,iBAAO,WAAW;AAAA,QACpB,CAAC;AACD,YAAI,mBAAmB,MAAM,eAAe,cAAc,UAAU,YAAY,QAAQ,OAAO,eAAe,KAAK,CAAC,WAAW,QAAQ,OAAO,eAAe,KAAK,CAAC,eAAe,iBAAiB,MAAM,IAAI;AAC3M,6BAAmB;AAAA,QACrB;AACA,YAAI,oBAAoB,GAAG;AACzB,cAAI,yBAAyB,qBAAqB,MAAM,eAAe,SAAS,IAAI,IAAI,mBAAmB;AAC3G,cAAI,oBAAoB,MAAM,eAAe,sBAAsB;AACnE,4BAAkB,kBAAkB;AAAA,QACtC;AAAA,MACF;AAAA,IACF,OAAO;AACL,wBAAkB,iBAAiB,eAAe;AAAA,IACpD;AACA,QAAI,iBAAiB;AACnB,QAAE,eAAe;AACjB,eAAS,eAAe;AAAA,IAC1B;AAAA,EACF;AACA,MAAI,WAAW,SAAS,UAAU,GAAG;AACnC,QAAI,cAAc,CAAC,KAAK,eAAe,OAAO,mBAAmB,CAAC,MAAM,OAAO;AAC7E,QAAE,eAAe;AACjB,WAAK,WAAW;AAChB;AAAA,IACF;AACA,QAAI,WAAW,CAAC,GAAG;AACjB,eAAS,CAAC;AACV;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa,SAAS,YAAY,GAAG;AACvC,QAAI,SAAS,gBAAgB,CAAC;AAC9B,QAAI,mBAAmB,MAAM,KAAK,GAAG;AACnC;AAAA,IACF;AACA,QAAI,eAAe,OAAO,yBAAyB,CAAC,GAAG;AACrD;AAAA,IACF;AACA,QAAI,eAAe,OAAO,mBAAmB,CAAC,GAAG;AAC/C;AAAA,IACF;AACA,MAAE,eAAe;AACjB,MAAE,yBAAyB;AAAA,EAC7B;AACA,MAAI,eAAe,SAAS,gBAAgB;AAC1C,QAAI,CAAC,MAAM,QAAQ;AACjB;AAAA,IACF;AACA,qBAAiB,aAAa,IAAI;AAClC,UAAM,yBAAyB,OAAO,oBAAoB,MAAM,WAAW;AACzE,eAAS,oBAAoB,CAAC;AAAA,IAChC,CAAC,IAAI,SAAS,oBAAoB,CAAC;AACnC,QAAI,iBAAiB,WAAW,cAAc,IAAI;AAClD,QAAI,iBAAiB,aAAa,kBAAkB;AAAA,MAClD,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AACD,QAAI,iBAAiB,cAAc,kBAAkB;AAAA,MACnD,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AACD,QAAI,iBAAiB,SAAS,YAAY;AAAA,MACxC,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AACD,QAAI,iBAAiB,WAAW,UAAU;AAAA,MACxC,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AACD,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB,SAAS,mBAAmB;AAChD,QAAI,CAAC,MAAM,QAAQ;AACjB;AAAA,IACF;AACA,QAAI,oBAAoB,WAAW,cAAc,IAAI;AACrD,QAAI,oBAAoB,aAAa,kBAAkB,IAAI;AAC3D,QAAI,oBAAoB,cAAc,kBAAkB,IAAI;AAC5D,QAAI,oBAAoB,SAAS,YAAY,IAAI;AACjD,QAAI,oBAAoB,WAAW,UAAU,IAAI;AACjD,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,IAAI,SAAS;AACX,aAAO,MAAM;AAAA,IACf;AAAA,IACA,IAAI,SAAS;AACX,aAAO,MAAM;AAAA,IACf;AAAA,IACA,UAAU,SAAS,SAAS,iBAAiB;AAC3C,UAAI,MAAM,QAAQ;AAChB,eAAO;AAAA,MACT;AACA,UAAI,aAAa,UAAU,iBAAiB,YAAY;AACxD,UAAI,iBAAiB,UAAU,iBAAiB,gBAAgB;AAChE,UAAI,oBAAoB,UAAU,iBAAiB,mBAAmB;AACtE,UAAI,CAAC,mBAAmB;AACtB,4BAAoB;AAAA,MACtB;AACA,YAAM,SAAS;AACf,YAAM,SAAS;AACf,YAAM,8BAA8B,IAAI;AACxC,UAAI,YAAY;AACd,mBAAW;AAAA,MACb;AACA,UAAI,mBAAmB,SAAS,oBAAoB;AAClD,YAAI,mBAAmB;AACrB,8BAAoB;AAAA,QACtB;AACA,qBAAa;AACb,YAAI,gBAAgB;AAClB,yBAAe;AAAA,QACjB;AAAA,MACF;AACA,UAAI,mBAAmB;AACrB,0BAAkB,MAAM,WAAW,OAAO,CAAC,EAAE,KAAK,kBAAkB,gBAAgB;AACpF,eAAO;AAAA,MACT;AACA,uBAAiB;AACjB,aAAO;AAAA,IACT;AAAA,IACA,YAAY,SAAS,WAAW,mBAAmB;AACjD,UAAI,CAAC,MAAM,QAAQ;AACjB,eAAO;AAAA,MACT;AACA,UAAI,UAAU,eAAe;AAAA,QAC3B,cAAc,OAAO;AAAA,QACrB,kBAAkB,OAAO;AAAA,QACzB,qBAAqB,OAAO;AAAA,MAC9B,GAAG,iBAAiB;AACpB,mBAAa,MAAM,sBAAsB;AACzC,YAAM,yBAAyB;AAC/B,sBAAgB;AAChB,YAAM,SAAS;AACf,YAAM,SAAS;AACf,uBAAiB,eAAe,IAAI;AACpC,UAAI,eAAe,UAAU,SAAS,cAAc;AACpD,UAAI,mBAAmB,UAAU,SAAS,kBAAkB;AAC5D,UAAI,sBAAsB,UAAU,SAAS,qBAAqB;AAClE,UAAI,cAAc,UAAU,SAAS,eAAe,yBAAyB;AAC7E,UAAI,cAAc;AAChB,qBAAa;AAAA,MACf;AACA,UAAI,qBAAqB,SAAS,sBAAsB;AACtD,cAAM,WAAW;AACf,cAAI,aAAa;AACf,qBAAS,mBAAmB,MAAM,2BAA2B,CAAC;AAAA,UAChE;AACA,cAAI,kBAAkB;AACpB,6BAAiB;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,eAAe,qBAAqB;AACtC,4BAAoB,mBAAmB,MAAM,2BAA2B,CAAC,EAAE,KAAK,oBAAoB,kBAAkB;AACtH,eAAO;AAAA,MACT;AACA,yBAAmB;AACnB,aAAO;AAAA,IACT;AAAA,IACA,OAAO,SAAS,QAAQ;AACtB,UAAI,MAAM,UAAU,CAAC,MAAM,QAAQ;AACjC,eAAO;AAAA,MACT;AACA,YAAM,SAAS;AACf,sBAAgB;AAChB,aAAO;AAAA,IACT;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,UAAI,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ;AAClC,eAAO;AAAA,MACT;AACA,YAAM,SAAS;AACf,0BAAoB;AACpB,mBAAa;AACb,aAAO;AAAA,IACT;AAAA,IACA,yBAAyB,SAAS,wBAAwB,mBAAmB;AAC3E,UAAI,kBAAkB,CAAC,EAAE,OAAO,iBAAiB,EAAE,OAAO,OAAO;AACjE,YAAM,aAAa,gBAAgB,IAAI,SAAS,SAAS;AACvD,eAAO,OAAO,YAAY,WAAW,IAAI,cAAc,OAAO,IAAI;AAAA,MACpE,CAAC;AACD,UAAI,MAAM,QAAQ;AAChB,4BAAoB;AAAA,MACtB;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,OAAK,wBAAwB,QAAQ;AACrC,SAAO;AACT;AAGA,SAAS,YAAY,QAAQ;AAC3B,MAAI;AACJ,MAAI;AACJ,SAAO,iBAAiB,WAAW,MAAM;AACvC,kBAAc;AACd,qBAAiB,SAAS;AAAA,EAC5B,CAAC;AACD,SAAO,MAAM,SAAS,CAAC,OAAO;AAC5B,QAAI,SAAS;AACb,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,OAAO,KAAK;AACV,iBAAS;AACT,eAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AACjB,aAAK,aAAa;AAClB,eAAO;AAAA,MACT;AAAA,MACA,WAAW;AACT,aAAK,aAAa;AAClB,eAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AACf,aAAK,eAAe;AACpB,eAAO;AAAA,MACT;AAAA,MACA,OAAO;AACL,eAAO,KAAK,eAAe;AAAA,MAC7B;AAAA,MACA,UAAU,KAAK;AACb,eAAO,YAAY,GAAG;AAAA,MACxB;AAAA,MACA,oBAAoB;AAClB,eAAO;AAAA,MACT;AAAA,MACA,cAAc;AACZ,eAAO;AAAA,MACT;AAAA,MACA,UAAU;AACR,eAAO;AAAA,MACT;AAAA,MACA,aAAa;AACX,YAAI,MAAM,QAAQ,MAAM;AACtB,iBAAO;AACT,eAAO,UAAU,QAAQ,EAAE,cAAc,OAAO,CAAC;AAAA,MACnD;AAAA,MACA,MAAM;AACJ,eAAO,KAAK,WAAW;AAAA,MACzB;AAAA,MACA,QAAQ,KAAK;AACX,YAAI,MAAM,KAAK,IAAI;AACnB,eAAO,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE,WAAW,GAAG;AAAA,MACxC;AAAA,MACA,OAAO,KAAK;AACV,YAAI,MAAM,KAAK,IAAI;AACnB,eAAO,IAAI,UAAU,IAAI,MAAM,EAAE,EAAE,CAAC,EAAE,WAAW,GAAG;AAAA,MACtD;AAAA,MACA,WAAW;AACT,eAAO,KAAK,IAAI,EAAE,CAAC;AAAA,MACrB;AAAA,MACA,UAAU;AACR,eAAO,KAAK,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC;AAAA,MAC/B;AAAA,MACA,UAAU;AACR,YAAI,OAAO,KAAK,IAAI;AACpB,YAAI,UAAU,SAAS;AACvB,YAAI,KAAK,QAAQ,OAAO,MAAM;AAC5B;AACF,YAAI,KAAK,gBAAgB,KAAK,QAAQ,OAAO,MAAM,KAAK,SAAS,GAAG;AAClE,iBAAO,KAAK,CAAC;AAAA,QACf;AACA,eAAO,KAAK,KAAK,QAAQ,OAAO,IAAI,CAAC;AAAA,MACvC;AAAA,MACA,cAAc;AACZ,YAAI,OAAO,KAAK,IAAI;AACpB,YAAI,UAAU,SAAS;AACvB,YAAI,KAAK,QAAQ,OAAO,MAAM;AAC5B;AACF,YAAI,KAAK,gBAAgB,KAAK,QAAQ,OAAO,MAAM,GAAG;AACpD,iBAAO,KAAK,MAAM,EAAE,EAAE,CAAC;AAAA,QACzB;AACA,eAAO,KAAK,KAAK,QAAQ,OAAO,IAAI,CAAC;AAAA,MACvC;AAAA,MACA,QAAQ;AACN,aAAK,MAAM,KAAK,SAAS,CAAC;AAAA,MAC5B;AAAA,MACA,OAAO;AACL,aAAK,MAAM,KAAK,QAAQ,CAAC;AAAA,MAC3B;AAAA,MACA,OAAO;AACL,aAAK,MAAM,KAAK,QAAQ,CAAC;AAAA,MAC3B;AAAA,MACA,WAAW;AACT,aAAK,MAAM,KAAK,YAAY,CAAC;AAAA,MAC/B;AAAA,MACA,OAAO;AACL,eAAO,KAAK,SAAS;AAAA,MACvB;AAAA,MACA,MAAM,KAAK;AACT,YAAI,CAAC;AACH;AACF,mBAAW,MAAM;AACf,cAAI,CAAC,IAAI,aAAa,UAAU;AAC9B,gBAAI,aAAa,YAAY,GAAG;AAClC,cAAI,MAAM,EAAE,eAAe,KAAK,WAAW,CAAC;AAAA,QAC9C,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,UAAU,QAAQ,OAAO;AAAA,IAC9B,CAAC,IAAI,EAAE,YAAY,UAAU,GAAG,EAAE,QAAQ,eAAe,QAAQ,MAAM;AACrE,UAAI,YAAY,cAAc,UAAU;AACxC,UAAI,WAAW;AACf,UAAI,UAAU;AAAA,QACZ,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,eAAe,MAAM;AAAA,MACvB;AACA,UAAI,UAAU,SAAS,aAAa,GAAG;AACrC,gBAAQ,eAAe;AAAA,MACzB,OAAO;AACL,YAAI,cAAc,GAAG,cAAc,aAAa;AAChD,YAAI;AACF,kBAAQ,eAAe;AAAA,MAC3B;AACA,UAAI,OAAO,gBAAgB,IAAI,OAAO;AACtC,UAAI,YAAY,MAAM;AAAA,MACtB;AACA,UAAI,uBAAuB,MAAM;AAAA,MACjC;AACA,YAAM,eAAe,MAAM;AACzB,kBAAU;AACV,oBAAY,MAAM;AAAA,QAClB;AACA,6BAAqB;AACrB,+BAAuB,MAAM;AAAA,QAC7B;AACA,aAAK,WAAW;AAAA,UACd,aAAa,CAAC,UAAU,SAAS,UAAU;AAAA,QAC7C,CAAC;AAAA,MACH;AACA,aAAO,MAAM,UAAU,CAAC,UAAU;AAChC,YAAI,aAAa;AACf;AACF,YAAI,SAAS,CAAC,UAAU;AACtB,cAAI,UAAU,SAAS,UAAU;AAC/B,mCAAuB,iBAAiB;AAC1C,cAAI,UAAU,SAAS,OAAO;AAC5B,wBAAY,SAAS,EAAE;AACzB,qBAAW,MAAM;AACf,iBAAK,SAAS;AAAA,UAChB,GAAG,EAAE;AAAA,QACP;AACA,YAAI,CAAC,SAAS,UAAU;AACtB,uBAAa;AAAA,QACf;AACA,mBAAW,CAAC,CAAC;AAAA,MACf,CAAC,CAAC;AACF,cAAQ,YAAY;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA,IAIA,CAAC,IAAI,EAAE,YAAY,UAAU,GAAG,EAAE,SAAS,MAAM;AAC/C,UAAI,UAAU,SAAS,OAAO,KAAK,SAAS,UAAU;AACpD,iBAAS,EAAE;AAAA,IACf;AAAA,EACF,CAAC;AACH;AACA,SAAS,SAAS,IAAI;AACpB,MAAI,QAAQ,CAAC;AACb,kBAAgB,IAAI,CAAC,YAAY;AAC/B,QAAI,QAAQ,QAAQ,aAAa,aAAa;AAC9C,YAAQ,aAAa,eAAe,MAAM;AAC1C,UAAM,KAAK,MAAM,SAAS,QAAQ,gBAAgB,aAAa,CAAC;AAAA,EAClE,CAAC;AACD,SAAO,MAAM;AACX,WAAO,MAAM;AACX,YAAM,IAAI,EAAE;AAAA,EAChB;AACF;AACA,SAAS,gBAAgB,IAAI,UAAU;AACrC,MAAI,GAAG,WAAW,SAAS,IAAI,KAAK,CAAC,GAAG;AACtC;AACF,QAAM,KAAK,GAAG,WAAW,QAAQ,EAAE,QAAQ,CAAC,YAAY;AACtD,QAAI,QAAQ,WAAW,EAAE,GAAG;AAC1B,sBAAgB,GAAG,YAAY,QAAQ;AAAA,IACzC,OAAO;AACL,eAAS,OAAO;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AACA,SAAS,mBAAmB;AAC1B,MAAI,WAAW,SAAS,gBAAgB,MAAM;AAC9C,MAAI,eAAe,SAAS,gBAAgB,MAAM;AAClD,MAAI,iBAAiB,OAAO,aAAa,SAAS,gBAAgB;AAClE,WAAS,gBAAgB,MAAM,WAAW;AAC1C,WAAS,gBAAgB,MAAM,eAAe,GAAG,cAAc;AAC/D,SAAO,MAAM;AACX,aAAS,gBAAgB,MAAM,WAAW;AAC1C,aAAS,gBAAgB,MAAM,eAAe;AAAA,EAChD;AACF;AAGA,IAAI,iBAAiB;", "names": []}