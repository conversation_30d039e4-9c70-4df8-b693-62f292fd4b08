import { Plugin } from 'vite';

interface VitePluginRestartOptions {
    /**
     * Enable glob support for watcher (it's disabled by Vite, but add this plugin will turn it on by default)
     *
     * @default true
     */
    glob?: boolean;
    /**
     * @default 500
     */
    delay?: number;
    /**
     * Array of files to watch, changes to those file will trigger a server restart
     */
    restart?: string | string[];
    /**
     * Array of files to watch, changes to those file will trigger a client full page reload
     */
    reload?: string | string[];
}
declare function VitePluginRestart(options?: VitePluginRestartOptions): Plugin;

export { type VitePluginRestartOptions, VitePluginRestart as default };
