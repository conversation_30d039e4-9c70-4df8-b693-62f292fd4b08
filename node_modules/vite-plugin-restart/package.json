{"name": "vite-plugin-restart", "type": "module", "version": "0.4.2", "packageManager": "pnpm@9.14.2", "description": "Custom files/globs to restart Vite server", "author": "antfu <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/vite-plugin-restart", "repository": {"type": "git", "url": "https://github.com/antfu/vite-plugin-restart"}, "bugs": "https://github.com/antfu/vite-plugin-restart/issues", "keywords": ["vite-plugin"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup src/index.ts --dts --format cjs,esm", "dev": "nr build --watch", "lint": "eslint .", "prepublishOnly": "nr build", "release": "bumpp && npm publish"}, "peerDependencies": {"vite": "^2.9.0 || ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0"}, "dependencies": {"micromatch": "^4.0.8"}, "devDependencies": {"@antfu/eslint-config": "^3.10.0", "@antfu/ni": "^0.23.1", "@types/debug": "^4.1.12", "@types/micromatch": "^4.0.9", "@types/node": "^22.10.0", "bumpp": "^9.8.1", "eslint": "^9.15.0", "rollup": "^4.27.4", "tsup": "^8.3.5", "typescript": "^5.7.2", "vite": "^6.0.1"}}